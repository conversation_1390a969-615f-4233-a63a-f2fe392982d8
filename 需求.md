帮我开发一个为 XWiki 17.4.3 设计的专业水印扩展，支持动态文本水印、占位符渲染、防复制功能和移动端兼容性。

创建xwiki扩展官方文档 https://www.xwiki.org/xwiki/bin/view/Documentation/DevGuide/Tutorials/CreatingExtensions/

在当前创建的空项目中  Building a XAR with Maven，这样可以在xwiki后台上传不用重启xwiki，减少测试等待时间

用xiwiki推荐的方式实现水印需求, 实现优雅且简洁。

### 组件版本
- XWiki 17.x
- Java 17.x
- Maven 3.x

### 核心功能
- **动态水印渲染**: 基于Canvas的高质量水印生成
- **占位符支持**: 支持 `${user}` 和 `${timestamp}` 占位符自动替换
- **灵活配置**: 可配置水印文本、样式、位置、透明度等参数
- **防复制保护**: 可选的内容保护功能，防止选择和复制
- **移动端适配**: 响应式设计，支持移动设备

### 配置参数

|=参数名|=类型|=说明|=默认值
|enabled|boolean|是否启用水印|false
|textTemplate|string|水印文本模板|"${user} - ${timestamp}"
|xSpacing|integer|水平间距（像素）|200
|ySpacing|integer|垂直间距（像素）|100  
|angle|integer|旋转角度（度）|-30
|opacity|float|透明度（0.0-1.0）|0.3
|fontSize|integer|字体大小（像素）|14
|antiCopy|boolean|是否启用防复制|false
|applyToMobile|boolean|是否应用到移动设备|true

#### 特性
1. 访问 `http://localhost:8080/bin/admin/XWiki/XWikiPreferences`
2. 在左侧导航栏找到 **"Watermark"** 分类下的 **"Watermark"**
3. 勾选 **"启用水印"** 选项
4. 根据需要调整配置参数
5. 点击 **"保存配置"** 按钮

**特性**：
- 多语言支持（中文/英文自动切换）
- 集成到XWiki标准管理界面
- 配置直接保存到XWikiPreferences，无需单独页面

### 约束
不要过度架构：功能优先、代码简洁、注释明确、适度抽象即可。
运行时依赖使用 compileOnly（XWiki 提供运行时类）。

最后给出README.md