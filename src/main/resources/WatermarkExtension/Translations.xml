<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.Translations" locale="">
  <web>WatermarkExtension</web>
  <name>Translations</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Extension Translations</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>plain/1.0</syntaxId>
  <hidden>true</hidden>
  <object>
    <name>WatermarkExtension.Translations</name>
    <number>0</number>
    <className>XWiki.TranslationDocumentClass</className>
    <guid>watermark-translations-en</guid>
    <class>
      <name>XWiki.TranslationDocumentClass</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <scope>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
        <customDisplay/>
        <disabled>0</disabled>
        <name>scope</name>
        <number>1</number>
        <prettyName>Scope</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </scope>
    </class>
    <property>
      <scope>WIKI</scope>
    </property>
  </object>
  <content>## Watermark Extension Translations
## English (Default) Translations

# Admin Interface
admin.watermark=Watermark
admin.watermark.description=Configure watermark settings for the wiki
admin.watermark.configuration=Watermark Configuration
watermark.admin.title=Watermark Configuration
watermark.admin.tooltip=Configure watermark settings for the wiki
watermark.admin.configuration=Watermark Configuration
watermark.admin.category=Watermark
watermark.admin.save=Save Configuration
watermark.admin.reset=Reset to Defaults
watermark.admin.preview=Preview Watermark

# Configuration Fields
watermark.admin.enabled=Enable Watermark
watermark.admin.enabled.help=Enable or disable the watermark display across the wiki

watermark.admin.textTemplate=Watermark Text Template
watermark.admin.textTemplate.help=Text template for the watermark. Use ${user} for current user and ${timestamp} for current time

watermark.admin.xSpacing=Horizontal Spacing
watermark.admin.xSpacing.help=Horizontal spacing between watermarks in pixels (50-500)

watermark.admin.ySpacing=Vertical Spacing
watermark.admin.ySpacing.help=Vertical spacing between watermarks in pixels (50-500)

watermark.admin.angle=Rotation Angle
watermark.admin.angle.help=Rotation angle of the watermark text in degrees (-180 to 180)

watermark.admin.opacity=Opacity
watermark.admin.opacity.help=Transparency level of the watermark (0.0 = transparent, 1.0 = opaque)

watermark.admin.fontSize=Font Size
watermark.admin.fontSize.help=Font size of the watermark text in pixels (8-48)

watermark.admin.antiCopy=Anti-Copy Protection
watermark.admin.antiCopy.help=Enable anti-copy protection to prevent content copying and right-click

watermark.admin.applyToMobile=Apply to Mobile Devices
watermark.admin.applyToMobile.help=Enable watermark display on mobile devices and tablets

# Messages
watermark.admin.save.success=Watermark configuration saved successfully
watermark.admin.reset.success=Watermark configuration reset to default values
watermark.admin.reset.confirm=Are you sure you want to reset all watermark settings to default values?

# Preview
watermark.admin.preview.placeholder=Watermark preview will appear here
watermark.admin.preview.help=This preview shows how the watermark will appear on pages
watermark.admin.preview.disabled=Watermark is disabled

# Configuration Class Properties
WatermarkExtension.WatermarkConfigClass_enabled=Enable Watermark
WatermarkExtension.WatermarkConfigClass_textTemplate=Text Template
WatermarkExtension.WatermarkConfigClass_xSpacing=Horizontal Spacing
WatermarkExtension.WatermarkConfigClass_ySpacing=Vertical Spacing
WatermarkExtension.WatermarkConfigClass_angle=Rotation Angle
WatermarkExtension.WatermarkConfigClass_opacity=Opacity
WatermarkExtension.WatermarkConfigClass_fontSize=Font Size
WatermarkExtension.WatermarkConfigClass_antiCopy=Anti-Copy Protection
WatermarkExtension.WatermarkConfigClass_applyToMobile=Apply to Mobile

# Error Messages
watermark.error.config.load=Failed to load watermark configuration
watermark.error.config.save=Failed to save watermark configuration
watermark.error.canvas.unsupported=Canvas is not supported in this browser
watermark.error.permission.denied=Permission denied to modify watermark settings

# Status Messages
watermark.status.enabled=Watermark is enabled
watermark.status.disabled=Watermark is disabled
watermark.status.loading=Loading watermark configuration...
watermark.status.saving=Saving watermark configuration...

# Help and Documentation
watermark.help.placeholders=Available placeholders: ${user} (current user), ${timestamp} (current time)
watermark.help.performance=Large spacing values and low opacity improve performance
watermark.help.mobile=Mobile optimization automatically adjusts watermark parameters
watermark.help.anticopy=Anti-copy protection may affect normal user interactions

# Validation Messages
watermark.validation.xSpacing=Value must be between 50 and 500
watermark.validation.ySpacing=Value must be between 50 and 500
watermark.validation.angle=Value must be between -180 and 180
watermark.validation.opacity=Value must be between 0.0 and 1.0
watermark.validation.fontSize=Value must be between 8 and 48

# WebHome Page Translations
watermark.home.title=Watermark Extension
watermark.home.description=This extension provides dynamic watermark functionality for XWiki pages.

# Features Section
watermark.home.features=Features
watermark.home.features.canvas=Dynamic Canvas Watermarks
watermark.home.features.canvas.desc=High-quality text watermarks rendered using HTML5 Canvas API
watermark.home.features.placeholder=Placeholder Support
watermark.home.features.placeholder.desc=Dynamic replacement of `${user}` and `${timestamp}` placeholders
watermark.home.features.config=Comprehensive Configuration
watermark.home.features.config.desc=8 configurable parameters for complete customization
watermark.home.features.anticopy=Anti-Copy Protection
watermark.home.features.anticopy.desc=Prevent content copying with configurable protection features
watermark.home.features.mobile=Mobile Optimization
watermark.home.features.mobile.desc=Responsive design with automatic mobile device adaptation
watermark.home.features.i18n=Multi-Language Support
watermark.home.features.i18n.desc=Built-in Chinese and English translations

# Documentation Section
watermark.home.documentation=Documentation
watermark.home.quickconfig=Quick Configuration
watermark.config.documentation=View Complete Documentation
watermark.home.documentation.params=Configuration Parameters
watermark.home.documentation.params.parameter=Parameter
watermark.home.documentation.params.type=Type
watermark.home.documentation.params.range=Range
watermark.home.documentation.params.default=Default
watermark.home.documentation.params.description=Description
watermark.home.documentation.params.enabled=Enable/disable watermark display
watermark.home.documentation.params.texttemplate=Watermark text with placeholder support
watermark.home.documentation.params.hspacing=Horizontal spacing between watermarks (px)
watermark.home.documentation.params.vspacing=Vertical spacing between watermarks (px)
watermark.home.documentation.params.angle=Text rotation angle (degrees)
watermark.home.documentation.params.opacity=Watermark transparency level
watermark.home.documentation.params.fontsize=Text font size (px)
watermark.home.documentation.params.anticopy=Enable anti-copy protection features
watermark.home.documentation.params.mobile=Show watermarks on mobile devices

# Placeholder Variables Section
watermark.home.placeholders=Placeholder Variables
watermark.home.placeholders.user=Replaced with current user's name
watermark.home.placeholders.timestamp=Replaced with current date and time

# Troubleshooting Section
watermark.home.troubleshooting=Troubleshooting
watermark.home.troubleshooting.desc=If watermarks are not appearing:
watermark.home.troubleshooting.config=Check Configuration
watermark.home.troubleshooting.config.desc=Ensure watermark is enabled in the configuration
watermark.home.troubleshooting.console=Browser Console
watermark.home.troubleshooting.console.desc=Check for JavaScript errors in browser developer tools
watermark.home.troubleshooting.network=Network Requests
watermark.home.troubleshooting.network.desc=Verify that watermark scripts are loading correctly
watermark.home.troubleshooting.canvas=Canvas Support
watermark.home.troubleshooting.canvas.desc=Ensure your browser supports HTML5 Canvas API

# Version Information Section
watermark.home.version=Version Information
watermark.home.version.extension=Extension Version
watermark.home.version.compatibility=XWiki Compatibility
watermark.home.version.updated=Last Updated</content>
</xwikidoc>
