<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.WatermarkSkinExtension" locale="">
  <web>WatermarkExtension</web>
  <name>WatermarkSkinExtension</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Skin Extension</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <object>
    <name>WatermarkExtension.WatermarkSkinExtension</name>
    <number>0</number>
    <className>XWiki.JavaScriptExtension</className>
    <guid>watermark-skin-extension</guid>
    <class>
      <name>XWiki.JavaScriptExtension</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <cache>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>cache</name>
        <number>5</number>
        <prettyName>Caching policy</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>long|short|default|forbid</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </cache>
      <code>
        <contenttype>PureText</contenttype>
        <disabled>0</disabled>
        <editor>PureText</editor>
        <name>code</name>
        <number>2</number>
        <prettyName>Code</prettyName>
        <rows>20</rows>
        <size>50</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.TextAreaClass</classType>
      </code>
      <name>
        <disabled>0</disabled>
        <name>name</name>
        <number>1</number>
        <prettyName>Name</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
      </name>
      <parse>
        <disabled>0</disabled>
        <displayFormType>select</displayFormType>
        <displayType>yesno</displayType>
        <name>parse</name>
        <number>4</number>
        <prettyName>Parse content</prettyName>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      </parse>
      <use>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>use</name>
        <number>3</number>
        <prettyName>Use this extension</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>currentPage|onDemand|always</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </use>
    </class>
    <property>
      <cache>forbid</cache>
    </property>
    <property>
      <code><![CDATA[
## ---------------- Velocity Start (parsed because parse=1) ----------------
#if("$!xcontext.user" == "")
  #set($wmUserRef = "XWikiGuest")
#else
  #set($wmUserRef = "$xcontext.user")
#end
#set($wmUserPretty = $xwiki.getUserName($wmUserRef, false))  ## false=纯文本

/* 服务端注入的上下文，前端直接读取 */
window.XWikiWatermarkCtx = {
  userRef: "$escapetool.javascript($wmUserRef)",      // 用户引用
  user: "$escapetool.javascript($wmUserPretty)",      // 展示名
  isGuest: #if($wmUserRef == "XWikiGuest") true #else false #end
};
## ---------------- Velocity End ----------------


/* XWiki Watermark Extension - JavaScript Implementation */
(function() {
  'use strict';

  // XWiki Watermark Engine
  window.XWikiWatermarkEngine = {
    config: {
      enabled: false,  // Will be overridden by loaded config
      textTemplate: '${user} - ${timestamp}',
      xSpacing: 200,
      ySpacing: 100,
      angle: -30,
      opacity: 0.3,
      fontSize: 14,
      antiCopy: false,
      applyToMobile: true
    },
    
    canvas: null,
    ctx: null,
    
    // Initialize watermark system
    init: function() {
      this.loadConfig();
    },
    
    // Load configuration from WatermarkConfiguration or XWikiPreferences
    loadConfig: function() {
      var self = this;

      // Try to get config from global variable first (set by admin page)
      if (window.watermarkConfig) {
        console.log('Using global watermark config:', window.watermarkConfig);
        this.updateConfig(window.watermarkConfig);
        return;
      }

      console.log('Loading watermark config via REST API...');

      // Try WatermarkConfiguration page first (priority 1)
      this.loadConfigFromPage('WatermarkExtension', 'WatermarkConfiguration', function(success) {
        if (!success) {
          // Fallback to XWikiPreferences (priority 2) for backward compatibility
          console.log('WatermarkConfiguration not found, trying XWikiPreferences...');
          self.loadConfigFromPage('XWiki', 'XWikiPreferences', function(success) {
            if (!success) {
              // Try alternative script method if REST API fails
              console.log('XWikiPreferences access failed, trying alternative method...');
              self.loadConfigViaScript();
            }
          });
        }
      });
    },

    // Load configuration from a specific page
    loadConfigFromPage: function(space, page, callback) {
      var self = this;
      var url = '/rest/wikis/xwiki/spaces/' + space + '/pages/' + page + '/objects/WatermarkExtension.WatermarkConfigClass';

      console.log('Trying to load config from:', url);

      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.setRequestHeader('Accept', 'application/json');
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            try {
              var data = JSON.parse(xhr.responseText);
              console.log('REST API response from ' + space + '.' + page + ':', data);
              if (data.objectSummaries && data.objectSummaries.length > 0) {
                console.log('Found config object in ' + space + '.' + page + ', loading details...');
                self.getConfigObjectFromPage(data.objectSummaries[0], space, page);
                callback(true);
                return;
              } else {
                console.log('No config objects found in ' + space + '.' + page);
                callback(false);
                return;
              }
            } catch (e) {
              console.warn('Failed to parse REST API response from ' + space + '.' + page + ':', e);
              callback(false);
              return;
            }
          } else if (xhr.status === 401) {
            console.log('REST API access denied (401) for ' + space + '.' + page);
            callback(false);
            return;
          } else {
            console.warn('REST API request failed for ' + space + '.' + page + ' with status:', xhr.status);
            callback(false);
            return;
          }
        }
      };
      xhr.send();
    },

    // Alternative method to load config via script tag (works for anonymous users)
    loadConfigViaScript: function() {
      var self = this;
      console.log('Loading config via script method...');

      // Create a script tag to load config from a public endpoint
      var script = document.createElement('script');
      script.src = '/bin/get/WatermarkExtension/WatermarkConfigProvider?outputSyntax=plain&xpage=plain';
      script.onload = function() {
        if (window.xwikiWatermarkPublicConfig) {
          console.log('Config loaded via script:', window.xwikiWatermarkPublicConfig);
          self.updateConfig(window.xwikiWatermarkPublicConfig);
        } else {
          console.log('No public config found, using defaults');
          self.updateConfig(self.config);
        }
      };
      script.onerror = function() {
        console.log('Failed to load config via script, using defaults');
        self.updateConfig(self.config);
      };
      document.head.appendChild(script);
    },

    // Get detailed config object from a specific page
    getConfigObjectFromPage: function(objectSummary, space, page) {
      var self = this;
      // Use the number property instead of the full ID
      var objectNumber = objectSummary.number || 0;
      var url = '/rest/wikis/xwiki/spaces/' + space + '/pages/' + page + '/objects/WatermarkExtension.WatermarkConfigClass/' + objectNumber;
      console.log('Loading config object from:', url);

      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.setRequestHeader('Accept', 'application/json');
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            try {
              var obj = JSON.parse(xhr.responseText);
              console.log('Config object loaded from ' + space + '.' + page + ':', obj);

              // Convert properties array to object for easier access
              var props = {};
              if (obj.properties && Array.isArray(obj.properties)) {
                obj.properties.forEach(function(prop) {
                  props[prop.name] = prop.value;
                });
              }
              console.log('Properties parsed:', props);

              var config = {
                enabled: props.enabled === '1' || props.enabled === 1,
                textTemplate: props.textTemplate || '${user} - ${timestamp}',
                xSpacing: parseInt(props.xSpacing) || 200,
                ySpacing: parseInt(props.ySpacing) || 100,
                angle: parseInt(props.angle) || -30,
                opacity: parseFloat(props.opacity) || 0.3,
                fontSize: parseInt(props.fontSize) || 14,
                antiCopy: props.antiCopy === '1' || props.antiCopy === 1,
                applyToMobile: props.applyToMobile === '1' || props.applyToMobile === 1
              };
              console.log('Parsed config from ' + space + '.' + page + ':', config);
              self.updateConfig(config);
            } catch (e) {
              console.error('Failed to parse watermark config from ' + space + '.' + page + ':', e);
              self.updateConfig(self.config);
            }
          } else {
            console.error('Failed to load watermark config object from ' + space + '.' + page + ', status:', xhr.status, 'URL:', url);
            self.updateConfig(self.config);
          }
        }
      };
      xhr.send();
    },

    // Get detailed config object (backward compatibility)
    getConfigObject: function(objectSummary) {
      this.getConfigObjectFromPage(objectSummary, 'XWiki', 'XWikiPreferences');
    },

    // Update configuration and apply watermark
    updateConfig: function(newConfig) {
      this.config = Object.assign(this.config, newConfig);
      console.log('Watermark config updated:', this.config);

      if (this.config.enabled) {
        console.log('Watermark is enabled, creating watermark...');
        this.createWatermark();
        if (this.config.antiCopy) {
          this.enableAntiCopy();
        }
      } else {
        console.log('Watermark is disabled');
        this.removeWatermark();
        this.disableAntiCopy();
      }
    },

    // Create watermark canvas
    createWatermark: function() {
      // Check mobile device
      var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      if (isMobile && !this.config.applyToMobile) {
        return;
      }

      // Remove existing watermark
      this.removeWatermark();

      // Create canvas with enhanced protection
      this.canvas = document.createElement('canvas');
      this.canvas.className = 'watermark-canvas wm-layer protection-layer';

      // Apply comprehensive protection styles
      this.canvas.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        pointer-events: none !important;
        z-index: 2147483647 !important;
        opacity: ${this.config.opacity} !important;
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        -webkit-user-drag: none !important;
        -khtml-user-drag: none !important;
        -moz-user-drag: none !important;
        -o-user-drag: none !important;
        user-drag: none !important;
      `;

      // Set canvas size
      this.canvas.width = window.innerWidth;
      this.canvas.height = window.innerHeight;

      // Get context
      this.ctx = this.canvas.getContext('2d');

      // Draw watermark
      this.drawWatermark();

      // Add protection attributes
      this.canvas.setAttribute('data-integrity', 'protected');
      this.canvas.setAttribute('data-timestamp', Date.now());
      this.canvas.setAttribute('data-watermark', 'true');

      // Add to page
      document.body.appendChild(this.canvas);

      // Apply additional protection after adding to DOM
      if (this.config.antiCopy) {
        this.reinforceCanvasProtection();
      }

      // Handle window resize with debouncing for better performance
      var self = this;
      var resizeTimeout;
      window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
          if (self.canvas) {
            self.canvas.width = window.innerWidth;
            self.canvas.height = window.innerHeight;
            self.drawWatermark();
          }
        }, 250); // 250ms debounce to reduce CPU usage during resize
      });
    },

    // Draw watermark on canvas
    drawWatermark: function() {
      if (!this.ctx) return;

      // Clear canvas
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      // Get processed text with placeholders replaced
      var text = this.processPlaceholders(this.config.textTemplate);

      // Set text properties
      this.ctx.font = this.config.fontSize + 'px Arial, sans-serif';
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';

      // Draw watermark grid
      for (var x = 0; x < this.canvas.width + this.config.xSpacing; x += this.config.xSpacing) {
        for (var y = 0; y < this.canvas.height + this.config.ySpacing; y += this.config.ySpacing) {
          this.ctx.save();

          // Move to position and rotate
          this.ctx.translate(x, y);
          this.ctx.rotate((this.config.angle * Math.PI) / 180);

          // Draw text
          this.ctx.fillText(text, 0, 0);

          this.ctx.restore();
        }
      }
    },

    // Process placeholder variables
    processPlaceholders: function(template) {
      var text = template;

      // Replace ${user} with current user
      var currentUser = (window.XWikiWatermarkCtx && window.XWikiWatermarkCtx.user) || 'Guest';
      text = text.replace(/\$\{user\}/g, currentUser);

      // Replace ${timestamp} with current time
      text = text.replace(/\$\{timestamp\}/g, new Date().toLocaleString());

      return text;
    },

    // Remove watermark
    removeWatermark: function() {
      if (this.canvas && this.canvas.parentNode) {
        this.canvas.parentNode.removeChild(this.canvas);
      }
      this.canvas = null;
      this.ctx = null;
    },

    // Anti-copy protection state
    antiCopyState: {
      unifiedProtectionInterval: null,
      isDevToolsOpen: false,
      canvasObserver: null,
      lastDebuggerTime: 0,
      lastConsoleTime: 0
    },

    // Enable anti-copy protection
    enableAntiCopy: function() {
      document.body.classList.add('watermark-anticopy');

      // Disable right-click context menu
      document.addEventListener('contextmenu', this.preventContextMenu);

      // Enhanced keyboard shortcuts prevention
      document.addEventListener('keydown', this.preventKeyboardShortcuts);

      // Disable drag and drop (including images)
      document.addEventListener('dragstart', this.preventDragAndImageSave);

      // Disable text selection
      document.addEventListener('selectstart', this.preventSelection);

      // Start unified protection (combines debugger, devtools, and canvas protection)
      this.startUnifiedProtection();

      // Add CSS protection
      this.addAntiCopyStyles();

      // Disable print
      this.disablePrint();
    },

    // Disable anti-copy protection
    disableAntiCopy: function() {
      document.body.classList.remove('watermark-anticopy');

      document.removeEventListener('contextmenu', this.preventContextMenu);
      document.removeEventListener('keydown', this.preventKeyboardShortcuts);
      document.removeEventListener('dragstart', this.preventDragAndImageSave);
      document.removeEventListener('selectstart', this.preventSelection);

      // Stop unified protection
      this.stopUnifiedProtection();

      // Remove CSS protection
      this.removeAntiCopyStyles();

      // Re-enable print
      this.enablePrint();
    },

    // Enhanced keyboard shortcuts prevention (optimized for better UX)
    preventKeyboardShortcuts: function(e) {
      var isCtrlOrCmd = e.ctrlKey || e.metaKey;
      var isShift = e.shiftKey;
      var keyCode = e.keyCode || e.which;

      // Check if the target is an input element where some shortcuts should be allowed
      var isInputElement = e.target && (
        e.target.tagName === 'INPUT' ||
        e.target.tagName === 'TEXTAREA' ||
        e.target.contentEditable === 'true' ||
        e.target.isContentEditable
      );

      // Allow F5 (refresh) for better user experience - employees need to refresh pages
      if (keyCode === 116) { // F5
        return true;
      }

      // Copy/Cut/Select All shortcuts - block these for content protection
      if (isCtrlOrCmd && [65, 67, 88].includes(keyCode)) { // A (Select All), C (Copy), X (Cut)
        if (!isInputElement) { // Allow in input elements for normal editing
          e.preventDefault();
          return false;
        }
      }

      // Paste (V) - allow in input elements
      if (isCtrlOrCmd && keyCode === 86) { // V (Paste)
        if (!isInputElement) {
          e.preventDefault();
          return false;
        }
        return true;
      }

      // Save (S) and View Source (U) - block these
      if (isCtrlOrCmd && [83, 85].includes(keyCode)) { // S, U
        e.preventDefault();
        return false;
      }

      // Developer tools shortcuts - only block the most common ones
      if (keyCode === 123) { // F12
        e.preventDefault();
        return false;
      }

      // Developer tools with modifiers (more selective blocking)
      if (isCtrlOrCmd && isShift && [73, 74].includes(keyCode)) { // I (DevTools), J (Console)
        e.preventDefault();
        return false;
      }

      if (isCtrlOrCmd && keyCode === 85) { // U (View Source)
        e.preventDefault();
        return false;
      }

      return true;
    },

    // Prevent context menu
    preventContextMenu: function(e) {
      e.preventDefault();
      return false;
    },

    // Prevent drag start and image saving (combined function)
    preventDragAndImageSave: function(e) {
      e.preventDefault();
      return false;
    },

    // Prevent text selection (except in input elements)
    preventSelection: function(e) {
      // Allow selection in input elements
      var isInputElement = e.target && (
        e.target.tagName === 'INPUT' ||
        e.target.tagName === 'TEXTAREA' ||
        e.target.contentEditable === 'true' ||
        e.target.isContentEditable
      );

      if (isInputElement) {
        return true; // Allow selection in input elements
      }

      e.preventDefault();
      return false;
    },

    // Start unified protection (combines all detection methods)
    startUnifiedProtection: function() {
      var self = this;

      // Single interval for all protection checks (reduced frequency for better performance)
      this.antiCopyState.unifiedProtectionInterval = setInterval(function() {
        var now = performance.now();

        // Debugger detection (every 3 seconds to reduce CPU usage)
        if (now - self.antiCopyState.lastDebuggerTime > 3000) {
          self.antiCopyState.lastDebuggerTime = now;
          var start = performance.now();
          debugger; // This will pause execution if DevTools is open
          var end = performance.now();

          // Increased threshold to reduce false positives
          if (end - start > 200) {
            self.handleDevToolsDetected();
            return;
          }
        }

        // Console detection (every 5 seconds)
        if (now - self.antiCopyState.lastConsoleTime > 5000) {
          self.antiCopyState.lastConsoleTime = now;
          var start = performance.now();
          console.log('%c', 'color: transparent');
          var end = performance.now();

          if (end - start > 10) {
            self.handleDevToolsDetected();
            return;
          }
        }

        // Window size detection (more lenient threshold)
        var threshold = 200; // Increased from 160 to reduce false positives
        if (window.outerHeight - window.innerHeight > threshold ||
            window.outerWidth - window.innerWidth > threshold) {
          self.handleDevToolsDetected();
          return;
        }

        // Canvas integrity check
        if (self.config.enabled && self.config.antiCopy) {
          self.checkCanvasIntegrity();
        }
      }, 2000); // Check every 2 seconds instead of multiple 1-second intervals

      // Setup canvas mutation observer and DOM protection
      self.setupCanvasObserver();
      self.protectDOMManipulation();
    },

    // Stop unified protection
    stopUnifiedProtection: function() {
      if (this.antiCopyState.unifiedProtectionInterval) {
        clearInterval(this.antiCopyState.unifiedProtectionInterval);
        this.antiCopyState.unifiedProtectionInterval = null;
      }

      if (this.antiCopyState.canvasObserver) {
        this.antiCopyState.canvasObserver.disconnect();
        this.antiCopyState.canvasObserver = null;
      }
    },

    // Setup canvas mutation observer (extracted from unified protection)
    setupCanvasObserver: function() {
      var self = this;

      if (window.MutationObserver) {
        this.antiCopyState.canvasObserver = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              // Check if watermark canvas was removed
              var removedNodes = Array.from(mutation.removedNodes);
              var canvasRemoved = removedNodes.some(function(node) {
                return node.nodeType === Node.ELEMENT_NODE &&
                       (node.className === 'watermark-canvas' ||
                        node.querySelector && node.querySelector('.watermark-canvas'));
              });

              if (canvasRemoved) {
                console.warn('检测到水印被移除，正在恢复...');
                self.handleCanvasTampered();
              }
            }

            // Check if canvas attributes were modified
            if (mutation.type === 'attributes' &&
                mutation.target.className === 'watermark-canvas') {
              console.warn('检测到水印被修改，正在恢复...');
              self.handleCanvasTampered();
            }
          });
        });

        // Start observing
        this.antiCopyState.canvasObserver.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: true,
          attributeFilter: ['style', 'class']
        });
      }
    },

    // Handle developer tools detection (simplified)
    handleDevToolsDetected: function() {
      if (!this.antiCopyState.isDevToolsOpen) {
        this.antiCopyState.isDevToolsOpen = true;

        // Blur content to protect information
        document.body.style.filter = 'blur(10px)';
        document.body.style.pointerEvents = 'none';

        // Show warning message
        console.clear();
        console.log('%c检测到开发者工具已打开，内容已被保护', 'color: red; font-size: 20px;');

        // Add visual warning overlay
        this.showProtectionOverlay();
      }
    },

    // Show protection overlay
    showProtectionOverlay: function() {
      if (document.getElementById('watermark-protection-overlay')) return;

      var overlay = document.createElement('div');
      overlay.id = 'watermark-protection-overlay';
      overlay.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.8) !important;
        color: white !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 2147483647 !important;
        font-size: 24px !important;
        text-align: center !important;
      `;
      overlay.innerHTML = '<div>请关闭开发者工具后刷新页面<br><small style="font-size: 16px;">Please close developer tools and refresh the page</small></div>';
      document.body.appendChild(overlay);
    },

    // Add anti-copy CSS styles
    addAntiCopyStyles: function() {
      var style = document.createElement('style');
      style.id = 'watermark-anticopy-styles';
      style.textContent = `
        .watermark-anticopy {
          -webkit-user-select: none !important;
          -moz-user-select: none !important;
          -ms-user-select: none !important;
          user-select: none !important;
          -webkit-touch-callout: none !important;
          -webkit-tap-highlight-color: transparent !important;
        }

        .watermark-anticopy * {
          -webkit-user-select: none !important;
          -moz-user-select: none !important;
          -ms-user-select: none !important;
          user-select: none !important;
          -webkit-touch-callout: none !important;
        }

        /* Allow text selection in input elements */
        .watermark-anticopy input,
        .watermark-anticopy textarea,
        .watermark-anticopy [contenteditable="true"],
        .watermark-anticopy [contenteditable] {
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
          user-select: text !important;
          -webkit-touch-callout: default !important;
        }

        .watermark-anticopy img {
          -webkit-user-drag: none !important;
          -khtml-user-drag: none !important;
          -moz-user-drag: none !important;
          -o-user-drag: none !important;
          user-drag: none !important;
          pointer-events: none !important;
        }

        /* Hide scrollbars when DevTools might be open */
        .watermark-devtools-detected {
          overflow: hidden !important;
        }

        /* Disable print styles */
        @media print {
          .watermark-anticopy {
            display: none !important;
          }
          body {
            display: none !important;
          }
        }
      `;
      document.head.appendChild(style);
    },

    // Remove anti-copy CSS styles
    removeAntiCopyStyles: function() {
      var style = document.getElementById('watermark-anticopy-styles');
      if (style) {
        style.parentNode.removeChild(style);
      }
    },

    // Disable print functionality (with user-friendly message)
    disablePrint: function() {
      // Override print function with user-friendly message
      window.print = function() {
        alert('出于安全考虑，打印功能已被禁用。如需打印，请联系管理员。\nPrint function is disabled for security reasons. Please contact administrator if printing is needed.');
        return false;
      };

      // Disable Ctrl+P / Cmd+P
      document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 80) { // P key
          e.preventDefault();
          alert('出于安全考虑，打印功能已被禁用。如需打印，请联系管理员。\nPrint function is disabled for security reasons. Please contact administrator if printing is needed.');
          return false;
        }
      });
    },

    // Re-enable print functionality
    enablePrint: function() {
      // Restore original print function (if possible)
      delete window.print;
    },



    // Check canvas integrity
    checkCanvasIntegrity: function() {
      if (!this.config.enabled || !this.config.antiCopy) {
        return;
      }

      var canvas = document.querySelector('.watermark-canvas');
      if (!canvas) {
        console.warn('水印Canvas丢失，正在重新创建...');
        this.handleCanvasTampered();
        return;
      }

      // Check if canvas is visible and properly positioned
      var style = window.getComputedStyle(canvas);
      if (style.display === 'none' ||
          style.visibility === 'hidden' ||
          style.opacity === '0' ||
          parseInt(style.zIndex) < 9999) {
        console.warn('水印Canvas被隐藏或层级被修改，正在恢复...');
        this.handleCanvasTampered();
      }
    },

    // Handle canvas tampering
    handleCanvasTampered: function() {
      // Immediately recreate watermark
      this.createWatermark();

      // Apply stronger protection
      this.reinforceCanvasProtection();

      // Optional: Take more drastic measures
      // this.handleDevToolsDetected(); // Blur the page
    },

    // Reinforce canvas protection with stronger measures
    reinforceCanvasProtection: function() {
      if (!this.canvas) return;

      // Make canvas harder to select and modify
      this.canvas.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        pointer-events: none !important;
        z-index: 2147483647 !important;
        opacity: ${this.config.opacity} !important;
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
      `;

      // Add multiple class names to make it harder to target
      this.canvas.className = 'watermark-canvas wm-layer protection-layer';

      // Add data attributes for integrity checking
      this.canvas.setAttribute('data-integrity', 'protected');
      this.canvas.setAttribute('data-timestamp', Date.now());
    },

    // Simplified DOM protection (focused on canvas protection)
    protectDOMManipulation: function() {
      var self = this;

      // Store original methods
      var originalRemoveChild = Node.prototype.removeChild;
      var originalRemove = Element.prototype.remove;

      // Override removeChild (simplified)
      Node.prototype.removeChild = function(child) {
        if (child && child.className && child.className.includes('watermark-canvas')) {
          console.warn('阻止删除水印Canvas');
          self.handleCanvasTampered();
          return child; // Pretend to remove but don't actually remove
        }
        return originalRemoveChild.call(this, child);
      };

      // Override remove method (simplified)
      Element.prototype.remove = function() {
        if (this.className && this.className.includes('watermark-canvas')) {
          console.warn('阻止删除水印Canvas');
          self.handleCanvasTampered();
          return; // Don't actually remove
        }
        return originalRemove.call(this);
      };

      // Store references for cleanup
      this.originalDOMMethods = {
        removeChild: originalRemoveChild,
        remove: originalRemove
      };
    },
  };

  // Initialize watermark when DOM is ready
  function initWatermark() {
    if (window.XWikiWatermarkEngine) {
      window.XWikiWatermarkEngine.init();
    }
  }

  // Initialize
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWatermark);
  } else {
    initWatermark();
  }
})();

]]></code>
    </property>
    <property>
      <name>WatermarkEngine</name>
    </property>
    <property>
      <parse>1</parse>
    </property>
    <property>
      <use>always</use>
    </property>
  </object>
  <content></content>
</xwikidoc>
