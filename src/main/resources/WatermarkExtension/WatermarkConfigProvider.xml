<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.WatermarkConfigProvider" locale="">
  <web>WatermarkExtension</web>
  <name>WatermarkConfigProvider</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Configuration Provider</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <content><![CDATA[{{velocity}}
## Public watermark configuration provider
## This page provides watermark configuration for anonymous users
## who cannot access the REST API
## Note: Only basic watermark functionality is exposed, not sensitive configuration details
## Output JavaScript configuration
window.xwikiWatermarkPublicConfig = {
  enabled: true,
  textTemplate: '${user} - ${timestamp}',
  xSpacing: 200,
  ySpacing: 100,
  angle: -30,
  opacity: 0.3,
  fontSize: 14,
  antiCopy: true,
  applyToMobile: true
};
{{/velocity}}
]]></content>
</xwikidoc>
